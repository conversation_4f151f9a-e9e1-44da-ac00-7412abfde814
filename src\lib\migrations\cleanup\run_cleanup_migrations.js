#!/usr/bin/env node

/**
 * Database Cleanup Migration Runner
 * 
 * This script safely executes database cleanup migrations for the RainbowPaws application.
 * It removes unused database elements while preserving all active functionality.
 * 
 * Usage:
 *   node run_cleanup_migrations.js [--dry-run] [--rollback]
 * 
 * Options:
 *   --dry-run    Show what would be executed without making changes
 *   --rollback   Execute rollback migrations instead of cleanup
 *   --verify     Run only verification script
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Configuration
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'rainbow_paws',
  port: parseInt(process.env.DB_PORT || '3306'),
  multipleStatements: true
};

// Migration files in execution order
const CLEANUP_MIGRATIONS = [
  '001_remove_unused_bookings_table.sql',
  '002_remove_unused_columns.sql',
  '003_verify_cleanup.sql'
];

const ROLLBACK_MIGRATIONS = [
  'rollback_002_restore_columns.sql',
  'rollback_001_restore_bookings_table.sql'
];

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const isRollback = args.includes('--rollback');
const isVerifyOnly = args.includes('--verify');

/**
 * Create database connection
 */
async function createConnection() {
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Database connection established');
    return connection;
  } catch (error) {
    console.error('❌ Failed to connect to database:', error.message);
    process.exit(1);
  }
}

/**
 * Read and parse SQL file
 */
function readSqlFile(filename) {
  const filePath = path.join(__dirname, filename);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Migration file not found: ${filename}`);
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Split into statements, removing comments and empty lines
  const statements = content
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'))
    .filter(stmt => !stmt.match(/^\s*\/\*[\s\S]*?\*\/\s*$/)); // Remove block comments
  
  return statements;
}

/**
 * Execute a single SQL statement
 */
async function executeStatement(connection, statement, filename) {
  try {
    const [results] = await connection.execute(statement);
    
    // Log results for SELECT statements
    if (statement.trim().toUpperCase().startsWith('SELECT')) {
      if (Array.isArray(results) && results.length > 0) {
        console.log(`   📊 Query result:`, results[0]);
      }
    }
    
    return { success: true, results };
  } catch (error) {
    console.error(`   ❌ Error in ${filename}:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Execute a migration file
 */
async function executeMigration(connection, filename) {
  console.log(`\n🔄 Executing: ${filename}`);
  
  if (isDryRun) {
    console.log(`   🔍 DRY RUN: Would execute ${filename}`);
    const statements = readSqlFile(filename);
    console.log(`   📝 Contains ${statements.length} SQL statements`);
    return { success: true };
  }
  
  try {
    const statements = readSqlFile(filename);
    let successCount = 0;
    let errorCount = 0;
    
    for (const statement of statements) {
      if (statement.trim()) {
        const result = await executeStatement(connection, statement, filename);
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
        }
      }
    }
    
    if (errorCount === 0) {
      console.log(`   ✅ Migration completed successfully (${successCount} statements)`);
      return { success: true };
    } else {
      console.log(`   ⚠️  Migration completed with errors (${successCount} success, ${errorCount} errors)`);
      return { success: false, errorCount };
    }
    
  } catch (error) {
    console.error(`   ❌ Failed to execute migration:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Prompt user for confirmation
 */
async function promptConfirmation(message) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

/**
 * Pre-migration safety checks
 */
async function performSafetyChecks(connection) {
  console.log('\n🛡️  Performing safety checks...');
  
  try {
    // Check if bookings table has data
    const [bookingsCount] = await connection.execute('SELECT COUNT(*) as count FROM bookings');
    if (bookingsCount[0].count > 0) {
      console.log(`   ⚠️  WARNING: bookings table has ${bookingsCount[0].count} records`);
      return false;
    } else {
      console.log('   ✅ bookings table is empty (safe to remove)');
    }

    // Check if user_reports table has data
    const [reportsCount] = await connection.execute('SELECT COUNT(*) as count FROM user_reports');
    if (reportsCount[0].count > 0) {
      console.log(`   ⚠️  WARNING: user_reports table has ${reportsCount[0].count} records`);
      return false;
    } else {
      console.log('   ✅ user_reports table is empty (safe to remove)');
    }
    
    // Check if core tables exist
    const [coreTablesCount] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME IN ('users', 'service_providers', 'service_bookings')
    `);
    
    if (coreTablesCount[0].count < 3) {
      console.log('   ❌ Core tables missing - database may be corrupted');
      return false;
    } else {
      console.log('   ✅ Core tables present');
    }
    
    // Check for foreign key constraints on tables to be removed
    const [fkCount] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.KEY_COLUMN_USAGE
      WHERE CONSTRAINT_SCHEMA = DATABASE()
      AND REFERENCED_TABLE_NAME IN ('bookings', 'user_reports')
    `);

    if (fkCount[0].count > 0) {
      console.log(`   ⚠️  WARNING: ${fkCount[0].count} foreign keys reference tables to be removed`);
      return false;
    } else {
      console.log('   ✅ No foreign key dependencies on tables to be removed');
    }
    
    return true;
  } catch (error) {
    console.error('   ❌ Safety check failed:', error.message);
    return false;
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 RainbowPaws Database Cleanup Migration Runner');
  console.log('================================================');
  
  if (isDryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }
  
  if (isRollback) {
    console.log('🔄 ROLLBACK MODE - Restoring removed elements');
  }
  
  if (isVerifyOnly) {
    console.log('✅ VERIFY MODE - Running verification only');
  }
  
  // Create database connection
  const connection = await createConnection();
  
  try {
    // Determine which migrations to run
    let migrations;
    if (isVerifyOnly) {
      migrations = ['003_verify_cleanup.sql'];
    } else if (isRollback) {
      migrations = ROLLBACK_MIGRATIONS;
    } else {
      migrations = CLEANUP_MIGRATIONS;
    }
    
    // Perform safety checks for cleanup migrations
    if (!isRollback && !isVerifyOnly && !isDryRun) {
      const safetyPassed = await performSafetyChecks(connection);
      if (!safetyPassed) {
        console.log('\n❌ Safety checks failed. Aborting migration.');
        process.exit(1);
      }
      
      // Get user confirmation
      const confirmed = await promptConfirmation('\n⚠️  This will permanently remove unused database elements. Continue?');
      if (!confirmed) {
        console.log('Migration cancelled by user.');
        process.exit(0);
      }
    }
    
    // Execute migrations
    console.log(`\n📋 Executing ${migrations.length} migration(s)...`);
    
    let totalSuccess = 0;
    let totalErrors = 0;
    
    for (const migration of migrations) {
      const result = await executeMigration(connection, migration);
      if (result.success) {
        totalSuccess++;
      } else {
        totalErrors++;
      }
    }
    
    // Summary
    console.log('\n📊 Migration Summary');
    console.log('===================');
    console.log(`✅ Successful: ${totalSuccess}`);
    console.log(`❌ Errors: ${totalErrors}`);
    
    if (totalErrors === 0) {
      console.log('\n🎉 All migrations completed successfully!');
      
      if (!isDryRun && !isVerifyOnly) {
        console.log('\n📋 Next Steps:');
        console.log('1. Test application functionality');
        console.log('2. Monitor application logs for errors');
        console.log('3. Verify user authentication works');
        console.log('4. Test booking system');
        console.log('5. Check payment processing');
        
        if (!isRollback) {
          console.log('\n🔄 If issues occur, run rollback:');
          console.log('   node run_cleanup_migrations.js --rollback');
        }
      }
    } else {
      console.log('\n⚠️  Some migrations failed. Check the errors above.');
      process.exit(1);
    }
    
  } finally {
    await connection.end();
    console.log('\n🔌 Database connection closed');
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the migration
main().catch((error) => {
  console.error('❌ Migration failed:', error);
  process.exit(1);
});
