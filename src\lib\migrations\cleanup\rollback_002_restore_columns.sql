-- Rollback Migration: Restore removed columns
-- Description: Restores columns that were removed during cleanup
-- Risk Level: LOW - Adds back unused columns with default values
-- Date: 2025-06-28
-- Author: Database Cleanup Analysis

-- ============================================================================
-- ROLLBACK SAFETY CHECKS
-- ============================================================================

-- Check if columns already exist (avoid conflicts)
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    'Column already exists' as status
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND (
    (TABLE_NAME = 'users' AND COLUMN_NAME = 'last_restriction_check') OR
    (TABLE_NAME = 'service_providers' AND COLUMN_NAME = 'active_service_count')
);

-- ============================================================================
-- ROLLBACK EXECUTION
-- ============================================================================

-- Step 1: Restore last_restriction_check column to users table
-- Add the column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'last_restriction_check'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `users` ADD COLUMN `last_restriction_check` timestamp NULL DEFAULT NULL AFTER `restriction_status`',
    'SELECT "Column last_restriction_check already exists" as result'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 2: Restore active_service_count column to service_providers table
-- Add the column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'service_providers'
    AND COLUMN_NAME = 'active_service_count'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `service_providers` ADD COLUMN `active_service_count` int(11) DEFAULT 0 AFTER `government_id_path`',
    'SELECT "Column active_service_count already exists" as result'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- INDEX RESTORATION
-- ============================================================================

-- Restore the compound index on users table that included last_restriction_check
-- First check if the index exists
SET @index_exists = (
    SELECT COUNT(*)
    FROM information_schema.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'users'
    AND INDEX_NAME = 'idx_users_restriction_status'
);

-- Drop existing index and recreate with both columns
SET @sql = IF(@index_exists > 0,
    'ALTER TABLE `users` DROP INDEX `idx_users_restriction_status`, ADD INDEX `idx_users_restriction_status` (`restriction_status`, `last_restriction_check`)',
    'ALTER TABLE `users` ADD INDEX `idx_users_restriction_status` (`restriction_status`, `last_restriction_check`)'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- DATA INITIALIZATION (Optional)
-- ============================================================================

-- Initialize active_service_count with actual counts (if desired)
-- This is optional since the column was never used
/*
UPDATE service_providers sp
SET active_service_count = (
    SELECT COUNT(*)
    FROM service_packages pkg
    WHERE pkg.provider_id = sp.provider_id
    AND pkg.is_active = 1
);
*/

-- Note: last_restriction_check will remain NULL since it was never used

-- ============================================================================
-- ROLLBACK VERIFICATION
-- ============================================================================

-- Verify column restoration
SELECT 
    'COLUMN RESTORATION VERIFICATION' as check_type,
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND (
    (TABLE_NAME = 'users' AND COLUMN_NAME = 'last_restriction_check') OR
    (TABLE_NAME = 'service_providers' AND COLUMN_NAME = 'active_service_count')
)
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Verify index restoration
SELECT 
    'INDEX RESTORATION VERIFICATION' as check_type,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'users'
AND INDEX_NAME = 'idx_users_restriction_status'
ORDER BY SEQ_IN_INDEX;

-- Check column data
SELECT 
    'users.last_restriction_check data' as check_type,
    COUNT(*) as total_rows,
    COUNT(last_restriction_check) as non_null_values
FROM users;

SELECT 
    'service_providers.active_service_count data' as check_type,
    COUNT(*) as total_rows,
    AVG(active_service_count) as average_count,
    MAX(active_service_count) as max_count
FROM service_providers;

-- ============================================================================
-- MIGRATION HISTORY RECORD
-- ============================================================================

-- Record this rollback in the migration_history table
INSERT INTO migration_history (migration_name, executed_at, success, error_message)
VALUES ('rollback_002_restore_columns', NOW(), 1, 'Removed columns restored successfully');

-- ============================================================================
-- NOTES
-- ============================================================================

/*
WHAT WAS RESTORED:
1. users.last_restriction_check - Timestamp column for restriction tracking
2. service_providers.active_service_count - Integer column for service counting
3. idx_users_restriction_status - Compound index including both restriction columns

COLUMN DETAILS:
- last_restriction_check: timestamp NULL DEFAULT NULL
- active_service_count: int(11) DEFAULT 0

DATA STATE AFTER ROLLBACK:
- last_restriction_check: All values will be NULL (column was unused)
- active_service_count: All values will be 0 (column was never updated)

POST-ROLLBACK ACTIONS:
1. Verify application still functions normally
2. Consider if these columns should be properly implemented
3. If not needed, remove again using cleanup migration

ROLLBACK REASON:
This rollback should only be used if:
- Application errors occur after column removal
- Future development requires these columns
- Database schema documentation needs to be complete

IMPLEMENTATION NOTES:
- If you want active_service_count to have real data, uncomment the UPDATE query
- The index is restored to match the original schema exactly
- All changes are backward compatible
*/
