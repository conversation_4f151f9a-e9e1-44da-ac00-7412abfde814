-- Rollback Migration: Restore bookings table and bookings_view
-- Description: Restores the bookings table and view if rollback is needed
-- Risk Level: LOW - Recreates unused elements (no data to restore)
-- Date: 2025-06-28
-- Author: Database Cleanup Analysis

-- ============================================================================
-- ROLLBACK SAFETY CHECKS
-- ============================================================================

-- Check if tables already exist (avoid conflicts)
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SAFE: bookings table does not exist, can proceed with restoration'
        ELSE 'WARNING: bookings table already exists'
    END as safety_check
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'bookings';

-- ============================================================================
-- ROLLBACK EXECUTION
-- ============================================================================

-- Step 1: Recreate the bookings table with original structure
CREATE TABLE IF NOT EXISTS `bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `service_provider_id` int(11) NOT NULL,
  `service_package_id` int(11) NOT NULL,
  `pet_id` int(11) DEFAULT NULL,
  `pet_name` varchar(255) DEFAULT NULL,
  `pet_type` varchar(100) DEFAULT NULL,
  `cause_of_death` text DEFAULT NULL,
  `pet_image_url` varchar(255) DEFAULT NULL,
  `booking_date` date DEFAULT NULL,
  `booking_time` time DEFAULT NULL,
  `status` enum('pending','confirmed','in_progress','completed','cancelled') DEFAULT 'pending',
  `special_requests` text DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT 'cash',
  `payment_status` enum('not_paid','partially_paid','paid','refunded') DEFAULT 'not_paid',
  `refund_id` int(11) DEFAULT NULL,
  `delivery_option` enum('pickup','delivery') DEFAULT 'pickup',
  `delivery_address` text DEFAULT NULL,
  `delivery_distance` float DEFAULT 0,
  `delivery_fee` decimal(10,2) DEFAULT 0.00,
  `total_price` decimal(10,2) DEFAULT NULL,
  `provider_name` varchar(255) DEFAULT NULL,
  `package_name` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `provider_id` int(11) GENERATED ALWAYS AS (`service_provider_id`) VIRTUAL,
  `package_id` int(11) GENERATED ALWAYS AS (`service_package_id`) VIRTUAL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `service_provider_id` (`service_provider_id`),
  KEY `service_package_id` (`service_package_id`),
  KEY `pet_id` (`pet_id`),
  KEY `status` (`status`),
  KEY `booking_date` (`booking_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Step 2: Recreate the bookings_view
CREATE OR REPLACE VIEW `bookings_view` AS
SELECT 
  `b`.`id` AS `id`,
  `b`.`user_id` AS `user_id`,
  `b`.`service_provider_id` AS `service_provider_id`,
  `b`.`service_package_id` AS `service_package_id`,
  `b`.`pet_id` AS `pet_id`,
  `b`.`pet_name` AS `pet_name`,
  `b`.`pet_type` AS `pet_type`,
  `b`.`cause_of_death` AS `cause_of_death`,
  `b`.`pet_image_url` AS `pet_image_url`,
  `b`.`booking_date` AS `booking_date`,
  `b`.`booking_time` AS `booking_time`,
  `b`.`status` AS `status`,
  `b`.`special_requests` AS `special_requests`,
  `b`.`payment_method` AS `payment_method`,
  `b`.`payment_status` AS `payment_status`,
  `b`.`refund_id` AS `refund_id`,
  `b`.`delivery_option` AS `delivery_option`,
  `b`.`delivery_address` AS `delivery_address`,
  `b`.`delivery_distance` AS `delivery_distance`,
  `b`.`delivery_fee` AS `delivery_fee`,
  `b`.`total_price` AS `total_price`,
  `b`.`provider_name` AS `provider_name`,
  `b`.`package_name` AS `package_name`,
  `b`.`quantity` AS `quantity`,
  `b`.`created_at` AS `created_at`,
  `b`.`updated_at` AS `updated_at`,
  `b`.`service_provider_id` AS `provider_id`,
  `b`.`service_package_id` AS `package_id`
FROM `bookings` `b`;

-- ============================================================================
-- ROLLBACK VERIFICATION
-- ============================================================================

-- Verify table restoration
SELECT 
    CASE 
        WHEN COUNT(*) = 1 THEN 'SUCCESS: bookings table restored'
        ELSE 'ERROR: bookings table not restored'
    END as verification_result
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'bookings';

-- Verify view restoration
SELECT 
    CASE 
        WHEN COUNT(*) = 1 THEN 'SUCCESS: bookings_view restored'
        ELSE 'ERROR: bookings_view not restored'
    END as verification_result
FROM information_schema.VIEWS
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'bookings_view';

-- Verify virtual columns
SELECT 
    COLUMN_NAME,
    GENERATION_EXPRESSION,
    IS_GENERATED
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'bookings'
AND IS_GENERATED = 'ALWAYS';

-- ============================================================================
-- MIGRATION HISTORY RECORD
-- ============================================================================

-- Record this rollback in the migration_history table
INSERT INTO migration_history (migration_name, executed_at, success, error_message)
VALUES ('rollback_001_restore_bookings_table', NOW(), 1, 'Bookings table and view restored');

-- ============================================================================
-- NOTES
-- ============================================================================

/*
WHAT WAS RESTORED:
1. bookings table - Complete table structure with all original columns
2. bookings_view - Database view that provides compatibility layer
3. Virtual columns - provider_id and package_id aliases

IMPORTANT NOTES:
- The restored table will be empty (no data to restore)
- The table structure matches the original schema exactly
- Virtual columns are recreated for compatibility
- All indexes are restored

POST-ROLLBACK ACTIONS:
1. Verify application still works with service_bookings table
2. Consider if bookings table should be properly implemented
3. If not needed, remove again using the cleanup migration

ROLLBACK REASON:
This rollback should only be used if:
- Application errors occur after cleanup
- Future development requires the bookings table
- Schema standardization is planned
*/
