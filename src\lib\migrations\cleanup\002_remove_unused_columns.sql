-- Migration: Remove unused columns from active tables
-- Description: Removes columns that are not used by the application
-- Risk Level: LOW - Columns are not referenced in application code
-- Date: 2025-06-28
-- Author: Database Cleanup Analysis

-- ============================================================================
-- SAFETY CHECKS
-- ============================================================================

-- Check if columns exist before attempting to remove them
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    DATA_TYPE
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND (
    (TABLE_NAME = 'users' AND COLUMN_NAME = 'last_restriction_check') OR
    (TABLE_NAME = 'service_providers' AND COLUMN_NAME = 'active_service_count')
)
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Check for any data in these columns (should be mostly NULL or default values)
SELECT 
    'users.last_restriction_check' as column_name,
    COUNT(*) as total_rows,
    COUNT(last_restriction_check) as non_null_values,
    COUNT(CASE WHEN last_restriction_check IS NOT NULL THEN 1 END) as has_data
FROM users
UNION ALL
SELECT 
    'service_providers.active_service_count' as column_name,
    COUNT(*) as total_rows,
    COUNT(active_service_count) as non_null_values,
    COUNT(CASE WHEN active_service_count != 0 THEN 1 END) as non_zero_values
FROM service_providers;

-- ============================================================================
-- MIGRATION EXECUTION
-- ============================================================================

-- Step 1: Remove last_restriction_check column from users table
-- This column is not used by the application's restriction system
-- The restriction system uses the user_restrictions table instead
ALTER TABLE `users` DROP COLUMN IF EXISTS `last_restriction_check`;

-- Step 2: Remove active_service_count column from service_providers table
-- This column was intended to track service count but is never updated
-- All records show 0 value and no application logic uses this field
ALTER TABLE `service_providers` DROP COLUMN IF EXISTS `active_service_count`;

-- ============================================================================
-- INDEX CLEANUP
-- ============================================================================

-- Remove any indexes that included the dropped columns
-- Check if the restriction status index needs to be recreated without last_restriction_check
SET @index_exists = (
    SELECT COUNT(*)
    FROM information_schema.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'users'
    AND INDEX_NAME = 'idx_users_restriction_status'
);

-- If the index existed and included last_restriction_check, recreate it with just restriction_status
SET @sql = IF(@index_exists > 0, 
    'ALTER TABLE users DROP INDEX IF EXISTS idx_users_restriction_status, ADD INDEX idx_users_restriction_status (restriction_status)',
    'SELECT "Index did not exist" as result'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify columns are removed
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: last_restriction_check column removed from users'
        ELSE 'ERROR: last_restriction_check column still exists in users'
    END as verification_result
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users'
AND COLUMN_NAME = 'last_restriction_check';

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: active_service_count column removed from service_providers'
        ELSE 'ERROR: active_service_count column still exists in service_providers'
    END as verification_result
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'service_providers'
AND COLUMN_NAME = 'active_service_count';

-- ============================================================================
-- MIGRATION HISTORY RECORD
-- ============================================================================

-- Record this migration in the migration_history table
INSERT INTO migration_history (migration_name, executed_at, success, error_message)
VALUES ('cleanup_002_remove_unused_columns', NOW(), 1, NULL);

-- ============================================================================
-- NOTES
-- ============================================================================

/*
WHAT WAS REMOVED:
1. users.last_restriction_check - Timestamp column not used by restriction system
2. service_providers.active_service_count - Counter column never updated

WHY IT'S SAFE:
- last_restriction_check: No application code reads or writes this field
- active_service_count: Always contains 0, never updated by application
- No foreign key constraints depend on these columns
- Application functionality is not affected

IMPACT:
- Reduces table size slightly
- Eliminates unused fields from schema
- Simplifies user and service provider data structures

ROLLBACK:
- Use rollback_002_restore_columns.sql if restoration is needed
- Note: Restored columns will have default/NULL values since they weren't used
*/
