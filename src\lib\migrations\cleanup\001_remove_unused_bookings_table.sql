-- Migration: Remove unused tables and views
-- Description: Removes unused tables (bookings, user_reports) and views that are not used by application
-- Risk Level: LOW - Tables are empty/unused by application
-- Date: 2025-06-28
-- Author: Database Cleanup Analysis

-- ============================================================================
-- SAFETY CHECKS
-- ============================================================================

-- Check if bookings table has any data (should be empty)
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SAFE: bookings table is empty'
        ELSE CONCAT('WARNING: bookings table has ', COUNT(*), ' records')
    END as safety_check
FROM bookings;

-- Check if user_reports table has any data (should be empty)
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SAFE: user_reports table is empty'
        ELSE CONCAT('WARNING: user_reports table has ', COUNT(*), ' records')
    END as safety_check
FROM user_reports;

-- Check if any foreign keys reference the bookings table
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IN ('bookings', 'user_reports');

-- ============================================================================
-- MIGRATION EXECUTION
-- ============================================================================

-- Step 1: Drop the bookings_view first (depends on bookings table)
DROP VIEW IF EXISTS `bookings_view`;

-- Step 2: Drop the unused user_reports table
-- Note: This table was created for user reporting system but never implemented
-- No application code uses this table and it contains no data
DROP TABLE IF EXISTS `user_reports`;

-- Step 3: Drop the unused bookings table
-- Note: This table was created as an attempt to standardize the schema
-- but the application continues to use service_bookings table instead
DROP TABLE IF EXISTS `bookings`;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify the tables and view are removed
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SUCCESS: bookings table removed'
        ELSE 'ERROR: bookings table still exists'
    END as verification_result
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'bookings';

SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SUCCESS: user_reports table removed'
        ELSE 'ERROR: user_reports table still exists'
    END as verification_result
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user_reports';

SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SUCCESS: bookings_view removed'
        ELSE 'ERROR: bookings_view still exists'
    END as verification_result
FROM information_schema.VIEWS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'bookings_view';

-- ============================================================================
-- MIGRATION HISTORY RECORD
-- ============================================================================

-- Record this migration in the migration_history table
INSERT INTO migration_history (migration_name, executed_at, success, error_message)
VALUES ('cleanup_001_remove_unused_tables', NOW(), 1, NULL);

-- ============================================================================
-- NOTES
-- ============================================================================

/*
WHAT WAS REMOVED:
1. bookings table - Empty table that was never used by the application
2. user_reports table - Empty table for user reporting system that was never implemented
3. bookings_view - Database view that referenced the unused bookings table

WHY IT'S SAFE:
- Both tables contain no data
- No application code references these tables
- The application uses service_bookings table instead of bookings
- No user reporting functionality exists in the application
- Foreign key constraints are properly handled

IMPACT:
- Reduces database schema complexity
- Eliminates confusion between bookings and service_bookings
- Removes unused user reporting infrastructure
- No functional impact on the application

ROLLBACK:
- Use rollback_001_restore_tables.sql if restoration is needed
- Note: Since tables were empty, no data will be lost/restored
*/
