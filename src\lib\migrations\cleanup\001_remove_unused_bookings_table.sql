-- Migration: Remove unused bookings table and bookings_view
-- Description: Removes the unused bookings table and its associated view that were never implemented
-- Risk Level: LOW - Table is empty and unused by application
-- Date: 2025-06-28
-- Author: Database Cleanup Analysis

-- ============================================================================
-- SAFETY CHECKS
-- ============================================================================

-- Check if bookings table has any data (should be empty)
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SAFE: bookings table is empty'
        ELSE CONCAT('WARNING: bookings table has ', COUNT(*), ' records')
    END as safety_check
FROM bookings;

-- Check if any foreign keys reference the bookings table
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME = 'bookings';

-- ============================================================================
-- MIGRATION EXECUTION
-- ============================================================================

-- Step 1: Drop the bookings_view first (depends on bookings table)
DROP VIEW IF EXISTS `bookings_view`;

-- Step 2: Drop the unused bookings table
-- Note: This table was created as an attempt to standardize the schema
-- but the application continues to use service_bookings table instead
DROP TABLE IF EXISTS `bookings`;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify the table and view are removed
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: bookings table removed'
        ELSE 'ERROR: bookings table still exists'
    END as verification_result
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'bookings';

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: bookings_view removed'
        ELSE 'ERROR: bookings_view still exists'
    END as verification_result
FROM information_schema.VIEWS
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'bookings_view';

-- ============================================================================
-- MIGRATION HISTORY RECORD
-- ============================================================================

-- Record this migration in the migration_history table
INSERT INTO migration_history (migration_name, executed_at, success, error_message)
VALUES ('cleanup_001_remove_unused_bookings_table', NOW(), 1, NULL);

-- ============================================================================
-- NOTES
-- ============================================================================

/*
WHAT WAS REMOVED:
1. bookings table - Empty table that was never used by the application
2. bookings_view - Database view that referenced the unused bookings table

WHY IT'S SAFE:
- The bookings table contains no data
- No application code references this table
- The application uses service_bookings table instead
- No foreign key constraints depend on this table

IMPACT:
- Reduces database schema complexity
- Eliminates confusion between bookings and service_bookings
- No functional impact on the application

ROLLBACK:
- Use rollback_001_restore_bookings_table.sql if restoration is needed
- Note: Since table was empty, no data will be lost/restored
*/
