-- Migration: Verification script for database cleanup
-- Description: Comprehensive verification that cleanup was successful and application integrity maintained
-- Risk Level: NONE - Read-only verification queries
-- Date: 2025-06-28
-- Author: Database Cleanup Analysis

-- ============================================================================
-- CLEANUP VERIFICATION
-- ============================================================================

-- Verify removed tables no longer exist
SELECT
    'REMOVED TABLES VERIFICATION' as check_type,
    CASE
        WHEN COUNT(*) = 0 THEN '✓ SUCCESS: No removed tables found'
        ELSE CONCAT('✗ ERROR: ', COUNT(*), ' removed tables still exist')
    END as result
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('bookings', 'user_reports');

-- Verify removed views no longer exist
SELECT 
    'REMOVED VIEWS VERIFICATION' as check_type,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ SUCCESS: No removed views found'
        ELSE CONCAT('✗ ERROR: ', COUNT(*), ' removed views still exist')
    END as result
FROM information_schema.VIEWS
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('bookings_view');

-- Verify removed columns no longer exist
SELECT 
    'REMOVED COLUMNS VERIFICATION' as check_type,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ SUCCESS: No removed columns found'
        ELSE CONCAT('✗ ERROR: ', COUNT(*), ' removed columns still exist')
    END as result
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND (
    (TABLE_NAME = 'users' AND COLUMN_NAME = 'last_restriction_check') OR
    (TABLE_NAME = 'service_providers' AND COLUMN_NAME = 'active_service_count')
);

-- ============================================================================
-- APPLICATION INTEGRITY VERIFICATION
-- ============================================================================

-- Verify core tables still exist and have expected structure
SELECT 
    'CORE TABLES VERIFICATION' as check_type,
    CASE 
        WHEN COUNT(*) = 8 THEN '✓ SUCCESS: All core tables present'
        ELSE CONCAT('✗ ERROR: Expected 8 core tables, found ', COUNT(*))
    END as result
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN (
    'users', 'service_providers', 'service_packages', 'service_bookings',
    'notifications', 'payment_transactions', 'pets', 'reviews'
);

-- Verify essential columns still exist
SELECT 
    'ESSENTIAL COLUMNS VERIFICATION' as check_type,
    CASE 
        WHEN COUNT(*) >= 20 THEN '✓ SUCCESS: Essential columns present'
        ELSE CONCAT('✗ WARNING: Only ', COUNT(*), ' essential columns found')
    END as result
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND (
    (TABLE_NAME = 'users' AND COLUMN_NAME IN ('user_id', 'email', 'role', 'status')) OR
    (TABLE_NAME = 'service_providers' AND COLUMN_NAME IN ('provider_id', 'user_id', 'name', 'application_status')) OR
    (TABLE_NAME = 'service_bookings' AND COLUMN_NAME IN ('id', 'user_id', 'provider_id', 'package_id', 'status')) OR
    (TABLE_NAME = 'notifications' AND COLUMN_NAME IN ('id', 'user_id', 'title', 'message')) OR
    (TABLE_NAME = 'payment_transactions' AND COLUMN_NAME IN ('id', 'booking_id', 'amount', 'status'))
);

-- ============================================================================
-- DATA INTEGRITY VERIFICATION
-- ============================================================================

-- Check that data in core tables is intact
SELECT 
    'USER DATA INTEGRITY' as check_type,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✓ SUCCESS: ', COUNT(*), ' users found')
        ELSE '✗ ERROR: No users found'
    END as result
FROM users;

SELECT 
    'SERVICE PROVIDER DATA INTEGRITY' as check_type,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✓ SUCCESS: ', COUNT(*), ' service providers found')
        ELSE '✗ WARNING: No service providers found'
    END as result
FROM service_providers;

SELECT 
    'BOOKING DATA INTEGRITY' as check_type,
    CASE 
        WHEN COUNT(*) >= 0 THEN CONCAT('✓ SUCCESS: ', COUNT(*), ' bookings found')
        ELSE '✗ ERROR: Cannot access service_bookings table'
    END as result
FROM service_bookings;

-- ============================================================================
-- FOREIGN KEY INTEGRITY VERIFICATION
-- ============================================================================

-- Verify foreign key constraints are still intact
SELECT 
    'FOREIGN KEY CONSTRAINTS' as check_type,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✓ SUCCESS: ', COUNT(*), ' foreign key constraints active')
        ELSE '✗ WARNING: No foreign key constraints found'
    END as result
FROM information_schema.KEY_COLUMN_USAGE
WHERE CONSTRAINT_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- ============================================================================
-- INDEX VERIFICATION
-- ============================================================================

-- Verify important indexes still exist
SELECT 
    'INDEX VERIFICATION' as check_type,
    CASE 
        WHEN COUNT(*) > 10 THEN CONCAT('✓ SUCCESS: ', COUNT(*), ' indexes found')
        ELSE CONCAT('✗ WARNING: Only ', COUNT(*), ' indexes found')
    END as result
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND INDEX_NAME != 'PRIMARY';

-- ============================================================================
-- MIGRATION HISTORY VERIFICATION
-- ============================================================================

-- Verify cleanup migrations are recorded
SELECT 
    'MIGRATION HISTORY' as check_type,
    CASE 
        WHEN COUNT(*) >= 2 THEN CONCAT('✓ SUCCESS: ', COUNT(*), ' cleanup migrations recorded')
        ELSE CONCAT('✗ WARNING: Only ', COUNT(*), ' cleanup migrations found')
    END as result
FROM migration_history
WHERE migration_name LIKE 'cleanup_%';

-- ============================================================================
-- SUMMARY REPORT
-- ============================================================================

-- Generate cleanup summary
SELECT 
    '=== CLEANUP SUMMARY ===' as summary,
    '' as details
UNION ALL
SELECT
    'Tables Removed:' as summary,
    'bookings, user_reports, bookings_view' as details
UNION ALL
SELECT 
    'Columns Removed:' as summary,
    'users.last_restriction_check, service_providers.active_service_count' as details
UNION ALL
SELECT 
    'Data Loss:' as summary,
    'None (removed elements were unused)' as details
UNION ALL
SELECT 
    'Application Impact:' as summary,
    'None (cleanup only removed unused elements)' as details;

-- Record verification completion
INSERT INTO migration_history (migration_name, executed_at, success, error_message)
VALUES ('cleanup_003_verify_cleanup', NOW(), 1, 'Cleanup verification completed successfully');

-- ============================================================================
-- NOTES
-- ============================================================================

/*
VERIFICATION CHECKLIST:
✓ Removed tables no longer exist
✓ Removed views no longer exist  
✓ Removed columns no longer exist
✓ Core application tables intact
✓ Essential columns present
✓ User data preserved
✓ Foreign key constraints maintained
✓ Indexes functioning
✓ Migration history updated

NEXT STEPS:
1. Test application functionality
2. Monitor for any errors in logs
3. Verify user authentication works
4. Test booking system
5. Confirm payment processing
6. Check notification system

If any issues are found, use the rollback scripts to restore removed elements.
*/
